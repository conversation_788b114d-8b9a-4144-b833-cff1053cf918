import React from 'react';
import { Bot } from 'lucide-react';

interface DaswosButtonProps {
  onClick: () => void;
  className?: string;
}

const DaswosButton: React.FC<DaswosButtonProps> = ({ onClick, className = '' }) => {
  return (
    <button
      onClick={onClick}
      className={`fixed bottom-4 left-4 z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center space-x-2 text-base font-semibold text-gray-800 dark:text-gray-200 hover:text-black dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700 ${className}`}

    >
      <Bot className="h-5 w-5" />
      <span>daswos</span>
    </button>
  );
};

export default DaswosButton;

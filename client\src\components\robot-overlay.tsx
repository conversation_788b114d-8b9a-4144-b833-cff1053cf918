import React, { useState, useCallback, useRef, useEffect } from 'react';
import RobotAnimation, { RobotAnimationRef } from './robot-animation';
import RobotControls from './robot-controls';
import FullscreenProductDisplay from './fullscreen-product-display';
import { RobotState } from '@/hooks/use-robot-animation';
import { useRobotContext } from '@/contexts/robot-context';
import { useToast } from '@/hooks/use-toast';
import { Button } from './ui/button';
import { Search, X } from 'lucide-react';
import { RobotPositioningAPI, type Position } from '@/services/robot-positioning';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSessionCoins } from '@/contexts/session-coins-context';
import { useAuth } from '@/hooks/use-auth';

interface RobotOverlayProps {
  className?: string;
}

const RobotOverlay: React.FC<RobotOverlayProps> = ({ className = '' }) => {
  const [robotState, setRobotState] = useState<RobotState>('idle');
  const [robotScale, setRobotScale] = useState<number>(0.3); // Default to 30% size
  const [isRobotActive, setIsRobotActive] = useState<boolean>(false);
  const [isRobotGlidingAway, setIsRobotGlidingAway] = useState<boolean>(false);
  const [showRobotControls, setShowRobotControls] = useState<boolean>(true);
  const [isFullScreenMode, setIsFullScreenMode] = useState<boolean>(false);

  const [showProductDisplay, setShowProductDisplay] = useState<boolean>(false);
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('');
  const [showSearchBar, setShowSearchBar] = useState<boolean>(false);
  const [showInformationPopup, setShowInformationPopup] = useState<boolean>(false);
  const [informationResults, setInformationResults] = useState<any[]>([]);
  const [isProductPopupOpen, setIsProductPopupOpen] = useState<boolean>(false);
  const [robotPosition, setRobotPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [lightsOn, setLightsOn] = useState<boolean>(false);
  const robotAnimationRef = useRef<RobotAnimationRef>(null);

  // Guessing game state
  const [isGuessMode, setIsGuessMode] = useState<boolean>(false);
  const [userGuess, setUserGuess] = useState<{ x: number; y: number } | null>(null);
  const [lastRobotPosition, setLastRobotPosition] = useState<{ x: number; y: number } | null>(null);
  const [showGuessResult, setShowGuessResult] = useState<boolean>(false);
  const [guessWasCorrect, setGuessWasCorrect] = useState<boolean>(false);
  const [guessDistance, setGuessDistance] = useState<number>(0);

  // Position tracking to prevent duplicates and ensure consistency
  const [currentRobotPosition, setCurrentRobotPosition] = useState<{ x: number; y: number } | null>(null);
  const [nextRobotPosition, setNextRobotPosition] = useState<{ x: number; y: number } | null>(null);

  // Guessing game API state
  const [gameStatus, setGameStatus] = useState<any>(null);
  const [showRewardModal, setShowRewardModal] = useState<boolean>(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState<boolean>(false);
  const [lastGuessResult, setLastGuessResult] = useState<any>(null);

  // Local fallback state for when database tables aren't ready
  const [localGuessCount, setLocalGuessCount] = useState<number>(() => {
    // Load from session storage for persistence across page refreshes
    const today = new Date().toISOString().split('T')[0];
    const stored = sessionStorage.getItem(`robotGuessCount_${today}`);
    return stored ? parseInt(stored, 10) : 0;
  });
  const [localSuccessCount, setLocalSuccessCount] = useState<number>(() => {
    const today = new Date().toISOString().split('T')[0];
    const stored = sessionStorage.getItem(`robotSuccessCount_${today}`);
    return stored ? parseInt(stored, 10) : 0;
  });

  const queryClient = useQueryClient();
  const { addSessionCoins } = useSessionCoins();
  const { user } = useAuth();

  // Fetch game status
  const { data: gameData, refetch: refetchGameStatus } = useQuery({
    queryKey: ['robotGuessingGameStatus'],
    queryFn: async () => {
      console.log('🔄 Fetching game status...');
      const response = await fetch('/api/robot-guessing-game/status');
      if (!response.ok) {
        console.error('❌ Failed to fetch game status:', response.status);
        throw new Error('Failed to fetch game status');
      }
      const data = await response.json();
      console.log('📊 Game status data:', data);
      return data;
    },
    refetchOnWindowFocus: false,
  });

  // Submit guess mutation
  const submitGuessMutation = useMutation({
    mutationFn: async (guessData: any) => {
      console.log('🎯 Submitting guess:', guessData);
      const response = await fetch('/api/robot-guessing-game/guess', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(guessData),
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Guess submission failed:', errorText);
        throw new Error('Failed to submit guess');
      }
      const result = await response.json();
      console.log('✅ Guess submission result:', result);
      return result;
    },
    onSuccess: (result) => {
      console.log('🎯 Guess mutation onSuccess:', result);
      setLastGuessResult(result);
      if (result.isSuccessful) {
        setShowRewardModal(true);
      }
      // Refetch game status to update counters
      console.log('🔄 Refetching game status after guess...');
      refetchGameStatus();
    },
    onError: (error) => {
      console.error('❌ Guess mutation error:', error);
    },
  });

  // Get next position from database-driven algorithm
  const getNextDatabasePosition = useCallback(async (): Promise<Position | null> => {
    try {
      const position = await RobotPositioningAPI.getNextPosition(
        window.innerWidth,
        window.innerHeight
      );

      if (position) {
        console.log('🎯 Received position from database algorithm:', position);
        return position;
      } else {
        console.log('⚠️ No position received from database, user may need algorithm assignment');
        // Ensure user has an algorithm assignment
        await RobotPositioningAPI.ensureAssignment();
        // Try again
        return await RobotPositioningAPI.getNextPosition(
          window.innerWidth,
          window.innerHeight
        );
      }
    } catch (error) {
      console.error('❌ Error getting database position:', error);
      return null;
    }
  }, []);

  // Pre-generate next position to ensure consistency between guessing and normal mode
  const getNextRobotPosition = useCallback(async (): Promise<Position | null> => {
    if (!nextRobotPosition) {
      // Get next position from database algorithm
      const newPosition = await getNextDatabasePosition();
      if (newPosition) {
        setNextRobotPosition(newPosition);
        console.log('🎯 Pre-generated next robot position from database:', newPosition);
        return newPosition;
      }
      return null;
    }
    return nextRobotPosition;
  }, [nextRobotPosition, getNextDatabasePosition]);

  // Use robot context and toast hook (must be declared before callbacks that use them)
  const { setIsRobotFullScreen } = useRobotContext();
  const { toast } = useToast();

  // Handle lights state change to reposition robot
  const handleLightsChange = useCallback(async (newLightsOn: boolean) => {
    console.log('💡 handleLightsChange called:', { newLightsOn, robotActive: isRobotActive, robotRef: !!robotAnimationRef.current, hasGuess: !!userGuess });
    setLightsOn(newLightsOn);

    // Move robot to new position every time lights are toggled (on OR off) and robot is active
    if (isRobotActive && robotAnimationRef.current) {
      // Use the pre-generated next position to ensure consistency
      const newPosition = await getNextRobotPosition();

      if (!newPosition) {
        console.error('❌ Failed to get next position for lights change');
        return;
      }

      console.log('💡 Moving robot to pre-determined position:', newPosition);

      // Check if ref is still valid after async operation
      if (!robotAnimationRef.current) {
        console.warn('⚠️ Robot ref became null during async operation');
        return;
      }

      // Use setPosition to move robot without changing center position
      robotAnimationRef.current.setPosition(newPosition.x, newPosition.y);

      // IMPORTANT: Always set scale to 30% (0.3) when lights change
      robotAnimationRef.current.setRobotScale(0.3);
      console.log('💡 Lights change - robot scale set to 30% (0.3)');
      setRobotPosition(newPosition);

      // Update position tracking
      setCurrentRobotPosition(newPosition);
      setNextRobotPosition(null); // Clear next position so a new one will be generated

      console.log('💡 Lights toggled - robot moved to position:', newPosition);

      // Check if user had made a guess - ONLY when lights are turned ON
      if (userGuess && newLightsOn) {
        const distance = Math.sqrt(
          Math.pow(newPosition.x - userGuess.x, 2) +
          Math.pow(newPosition.y - userGuess.y, 2)
        );
        const isCorrect = distance <= 100; // Within 100px is considered correct

        console.log('🎯 Guess check (lights ON):', {
          guess: userGuess,
          actual: newPosition,
          distance: Math.round(distance),
          isCorrect
        });

        // Update local counters for immediate feedback (works in both demo and live mode)
        const today = new Date().toISOString().split('T')[0];
        setLocalGuessCount(prev => {
          const newCount = prev + 1;
          sessionStorage.setItem(`robotGuessCount_${today}`, newCount.toString());
          console.log('📊 Updated local guess count:', newCount);
          return newCount;
        });
        if (isCorrect) {
          setLocalSuccessCount(prev => {
            const newCount = prev + 1;
            sessionStorage.setItem(`robotSuccessCount_${today}`, newCount.toString());
            console.log('🎉 Updated local success count:', newCount);
            return newCount;
          });

          // Add session coins for anonymous users only (20 coins = 20 DasWos coins = $20)
          if (!user) {
            addSessionCoins(20);
            console.log('💰 Added 20 session coins ($20) for anonymous user successful guess');
          }

          // Ensure purchase modal is closed after successful guess
          setShowPurchaseModal(false);
          console.log('🎉 Successful guess - ensuring purchase modal is closed');
        }

        // Submit guess to API for tracking and rewards (if gameId exists)
        if (gameData?.gameId) {
          console.log('🌐 Submitting guess to API...');
          submitGuessMutation.mutate({
            gameId: gameData.gameId,
            guessX: userGuess.x,
            guessY: userGuess.y,
            actualX: newPosition.x,
            actualY: newPosition.y,
          });
        } else {
          console.log('🎮 Demo mode - guess processed locally only');
          // In demo mode, show a simulated result
          setLastGuessResult({
            success: true,
            isSuccessful: isCorrect,
            distance: Math.round(distance),
            coinsAwarded: isCorrect ? 20 : 0,
            costPaid: 0,
            isAnonymous: true,
            mustSpendImmediately: false,
            message: isCorrect
              ? `Congratulations! You found Daswos and earned 20 DasWos coins! (Demo mode)`
              : `Close! You were ${Math.round(distance)} pixels away. Try again! (Demo mode)`,
            tablesNotReady: true
          });

          if (isCorrect) {
            setShowRewardModal(true);
          }
        }

        // Also manually update the local game data to show immediate feedback
        if (gameData) {
          console.log('🔄 Updating local game data for immediate feedback');
          // This will be overridden by the API response, but provides immediate visual feedback
          queryClient.setQueryData(['robotGuessingGameStatus'], (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              totalGuesses: (oldData.totalGuesses || 0) + 1,
              freeGuessesRemaining: Math.max(0, (oldData.freeGuessesRemaining || 0) - 1),
              successfulGuesses: isCorrect ? (oldData.successfulGuesses || 0) + 1 : (oldData.successfulGuesses || 0),
            };
          });
        }

        setGuessDistance(Math.round(distance));
        setGuessWasCorrect(isCorrect);
        setShowGuessResult(true);

        // IMPORTANT: Set the actual robot position for the guessing game marker
        setLastRobotPosition(newPosition);
        console.log('🎯 Set actual robot position for guess result:', newPosition);

        // Reset guess state ONLY after lights are turned ON and game is evaluated
        console.log('💡 Clearing guess after evaluation - user must make new guess for next round');
        setUserGuess(null);
        setIsGuessMode(false);

        // Auto-hide result immediately (no delay)
        setTimeout(() => {
          setShowGuessResult(false);
          setLastRobotPosition(null);
        }, 8000);

        // Force a re-render to update the counters immediately
        console.log('🔄 Forcing counter update...');
        setTimeout(() => {
          // This will trigger a re-render of components that depend on local counters
          setLocalGuessCount(prev => prev); // Trigger re-render
        }, 100);
      } else if (userGuess && !newLightsOn) {
        // If lights are turned OFF and user has a guess, preserve the guess for next ON toggle
        console.log('💡 Lights turned OFF - guess preserved for next ON toggle');
      }

      // Clear any existing guess results when lights are turned OFF
      if (!newLightsOn && showGuessResult) {
        console.log('💡 Lights turned OFF - clearing previous guess result popup');
        setShowGuessResult(false);
        // Keep lastRobotPosition so user can still see where robot was
      }
    }
  }, [getNextRobotPosition, isRobotActive, userGuess, toast, showGuessResult, user, addSessionCoins, gameData, submitGuessMutation, queryClient]);

  // Handle screen click for guessing game
  const handleScreenClick = useCallback((event: React.MouseEvent) => {
    if (isGuessMode && isFullScreenMode) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      console.log('🎯 User made guess at:', { x, y });
      setUserGuess({ x, y });
      setIsGuessMode(false);

      // Removed toast notification - user doesn't want these messages
    }
  }, [isGuessMode, isFullScreenMode, toast]);

  // Handle guess mode activation (new "?" button functionality)
  const handleGuessMode = useCallback(async () => {
    if (!isFullScreenMode) {
      toast({
        title: "Activate Daswos first",
        description: "You need to activate Daswos robot to play the guessing game!",
        variant: "destructive",
      });
      return;
    }

    // Check if user has any guesses left (free or paid)
    const freeGuessesRemaining = gameData?.freeGuessesRemaining ?? Math.max(0, 20 - localGuessCount);
    const canGuessForFree = freeGuessesRemaining > 0;
    const isLoggedIn = gameData?.isLoggedIn ?? false;
    const hasWallet = gameData?.hasWallet ?? false;

    // Only show purchase modal if user truly has no guesses left AND is not in the middle of a successful guess flow
    if (!canGuessForFree && !showRewardModal && !lastGuessResult?.isSuccessful) {
      console.log('🚫 No free guesses remaining, showing purchase modal');
      setShowPurchaseModal(true);
      return;
    }

    // If no free guesses left but user has wallet, allow paid guesses
    if (!canGuessForFree && isLoggedIn && hasWallet && !showRewardModal) {
      toast({
        title: "Extra Guess - 0.50 DasWos Coins 💰",
        description: `You've used your 20 free guesses today. This guess will cost 0.50 DasWos coins.`,
        duration: 5000,
      });
    }

    // Allow guessing even if out of free guesses if user just had a successful guess
    if (!canGuessForFree && (showRewardModal || lastGuessResult?.isSuccessful)) {
      console.log('🎉 Allowing continued play after successful guess');
    }

    setIsGuessMode(true);
    setUserGuess(null);
    setShowGuessResult(false);
    setLastRobotPosition(null); // Clear any previous robot position markers

    // Pre-generate the next position so user is guessing the actual next position
    await getNextRobotPosition();

    const remainingGuesses = gameData?.freeGuessesRemaining || 0;
    const guessInfo = remainingGuesses > 0
      ? `${remainingGuesses} free guesses remaining today!`
      : "Extra guess - 0.50 DasWos coins will be charged.";

    // Removed toast notification - user doesn't want these messages
    console.log(`🎯 Guessing mode activated: ${guessInfo}`);
  }, [isFullScreenMode, toast, getNextRobotPosition, gameData, showRewardModal, lastGuessResult, localGuessCount]);

  const handleStateChange = useCallback((newState: RobotState) => {
    setRobotState(newState);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState(newState);
    }

    // Show search bar only when search button is clicked (not voice commands)
    if (newState === 'search') {
      setShowSearchBar(true);
    }
  }, []);

  const handleScaleChange = useCallback((newScale: number) => {
    setRobotScale(newScale);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotScale(newScale);
    }
  }, []);

  // DISABLED: Center button interferes with guessing game positioning
  const handleCenter = useCallback(() => {
    // DISABLED: Don't center robot - interferes with guessing game
    console.log('🚫 Center button disabled for guessing game');
    // if (robotAnimationRef.current) {
    //   robotAnimationRef.current.centerRobot();
    // }
  }, []);

  const handleRoll = useCallback(() => {
    // Roll to a random position
    const x = Math.random() * window.innerWidth * 0.6 + window.innerWidth * 0.2;
    const y = Math.random() * window.innerHeight * 0.6 + window.innerHeight * 0.2;

    if (robotAnimationRef.current) {
      robotAnimationRef.current.rollToPosition(x, y);
    }
    setRobotState('roll');
  }, []);

  const handleRobotStateChange = useCallback((state: RobotState) => {
    setRobotState(state);
    // Note: Don't show search bar here - only show when search button is manually clicked
  }, []);

  // Track robot position for hologram placement
  const handleRobotPositionChange = useCallback((x: number, y: number) => {
    setRobotPosition({ x, y });
  }, []);

  const handleDaswosButtonClick = useCallback(async () => {
    setIsRobotActive(true);
    setShowRobotControls(true); // Show controls in full screen mode
    setIsFullScreenMode(true); // Start in full screen mode
    setIsRobotFullScreen(true); // Set global context to full screen mode

    // Get position from database algorithm for robot when activated
    if (robotAnimationRef.current) {
      const databasePosition = await getNextDatabasePosition();

      if (!databasePosition) {
        console.error('❌ Failed to get position for robot activation');
        return;
      }

      console.log('🤖 Robot activation - new database position:', databasePosition);

      // Check if ref is still valid after async operation
      if (!robotAnimationRef.current) {
        console.warn('⚠️ Robot ref became null during robot activation');
        return;
      }

      // Use setPosition to avoid changing center position for guessing game
      robotAnimationRef.current.setPosition(databasePosition.x, databasePosition.y);

      // IMPORTANT: Set scale to 30% (0.3) when robot is activated
      robotAnimationRef.current.setRobotScale(0.3);
      console.log('🤖 Robot scale set to 30% (0.3) on activation');

      // Update robot position state for hologram
      setRobotPosition(databasePosition);
      // Track this as the current position
      setCurrentRobotPosition(databasePosition);
      console.log('🤖 Robot activated at database position:', databasePosition, 'at 30% size');
    }

    // Dispatch robot activated event
    const activatedEvent = new CustomEvent('robotActivated');
    window.dispatchEvent(activatedEvent);

    // Robot activated - no popup needed
  }, [setIsRobotFullScreen, getNextDatabasePosition]);

  // Listen for global robot activation events
  useEffect(() => {
    const handleActivateRobot = () => {
      handleDaswosButtonClick();
    };

    window.addEventListener('activateRobot', handleActivateRobot);

    return () => {
      window.removeEventListener('activateRobot', handleActivateRobot);
    };
  }, [handleDaswosButtonClick]);

  const handleCloseRobotControls = useCallback(() => {
    setShowRobotControls(false);
  }, []);

  const handleFullScreenToggle = useCallback(async () => {
    const newFullScreenMode = !isFullScreenMode;
    setIsFullScreenMode(newFullScreenMode);
    setIsRobotFullScreen(newFullScreenMode); // Update global context

    if (robotAnimationRef.current) {
      if (newFullScreenMode) {
        // Get position from database algorithm for full screen mode
        const databasePosition = await getNextDatabasePosition();

        if (!databasePosition) {
          console.error('❌ Failed to get position for full screen mode');
          return;
        }

        console.log('🤖 Full screen mode - new database position:', databasePosition);

        // Check if ref is still valid after async operation
        if (!robotAnimationRef.current) {
          console.warn('⚠️ Robot ref became null during full screen toggle');
          return;
        }

        // Use setPosition to avoid changing center position for guessing game
        robotAnimationRef.current.setPosition(databasePosition.x, databasePosition.y);

        // IMPORTANT: Set scale to 30% (0.3) for full screen mode
        robotAnimationRef.current.setRobotScale(0.3);
        console.log('🤖 Full screen mode - robot scale set to 30% (0.3)');

        // Update robot position state for hologram
        setRobotPosition(databasePosition);
        // Track this as the current position
        setCurrentRobotPosition(databasePosition);
        console.log('🤖 Full screen mode - robot moved to database position:', databasePosition, 'at 30% size');
      } else {
        // Move back to bottom-right for compact mode
        const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
        const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)

        // Check if ref is still valid
        if (!robotAnimationRef.current) {
          console.warn('⚠️ Robot ref became null during compact mode switch');
          return;
        }

        // Use setPosition to avoid changing center position for guessing game
        robotAnimationRef.current.setPosition(targetX, targetY);
        // Update robot position state
        setRobotPosition({ x: targetX, y: targetY });
        // Track this as the current position
        setCurrentRobotPosition({ x: targetX, y: targetY });
      }
    }
  }, [isFullScreenMode, setIsRobotFullScreen, getNextDatabasePosition]);

  const handleGoAway = useCallback(() => {
    setIsRobotGlidingAway(true);
    setIsFullScreenMode(false); // Reset to compact mode
    setIsRobotFullScreen(false); // Reset global context

    if (robotAnimationRef.current) {
      robotAnimationRef.current.glideAway();
    }

    // After the glide animation completes, hide everything
    setTimeout(() => {
      setIsRobotActive(false);
      setIsRobotGlidingAway(false);

      // Dispatch robot deactivated event
      const deactivatedEvent = new CustomEvent('robotDeactivated');
      window.dispatchEvent(deactivatedEvent);
    }, 2000); // 2 seconds for the glide animation
  }, [setIsRobotFullScreen]);

  const handleCloseProductDisplay = useCallback(() => {
    setShowProductDisplay(false);
    setCurrentSearchQuery('');
    // Exit full screen mode when closing the product display
    setIsFullScreenMode(false);
    setIsRobotFullScreen(false);

    // Move robot back to compact position
    if (robotAnimationRef.current) {
      const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
      const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)
      // Use setPosition to avoid changing center position for guessing game
      robotAnimationRef.current.setPosition(targetX, targetY);
    }
  }, [setIsRobotFullScreen]);

  const handleClearSearch = useCallback(() => {
    setCurrentSearchQuery('');
    // Keep full screen mode but clear search to show random products
  }, []);

  const handleInformationSearch = useCallback(async (query: string) => {
    try {
      const response = await fetch(`/api/information?q=${encodeURIComponent(query)}&sphere=opensphere`);
      if (!response.ok) throw new Error('Failed to fetch information');
      const results = await response.json();
      setInformationResults(results);
      setShowInformationPopup(true);

      toast({
        title: 'Information Search Complete',
        description: `Found ${results.length} results for "${query}"`,
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Search Error',
        description: 'Failed to search for information',
        variant: 'destructive',
      });
    }
  }, [toast]);



  // Voice event listeners
  useEffect(() => {
    const handleVoiceStatus = (event: CustomEvent) => {
      const { status } = event.detail;

      // Update robot state based on voice status
      if (status === 'listening' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('search');
      } else if (status === 'speaking' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('talk');
      } else if (status === 'idle' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('idle');
      }
    };

    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse } = event.detail;

      if (userQuery) {
        toast({
          title: 'Voice Command Received',
          description: `"${userQuery}"`,
        });
      }

      if (aiResponse) {
        // In fullscreen mode, show enhanced AI responses
        if (isFullScreenMode) {
          toast({
            title: 'Daswos AI Response',
            description: typeof aiResponse === 'string' ? aiResponse : 'AI response received',
            duration: 5000,
          });
        }
      }
    };

    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query && isFullScreenMode) {
        // In fullscreen mode, show product display with search results
        setCurrentSearchQuery(query);
        setShowProductDisplay(true);

        // Search initiated - no popup needed
      }
    };

    const handleDaswosSearch = (event: CustomEvent) => {
      const { query } = event.detail;
      console.log('🔍 RobotOverlay received daswosSearch event:', query);
      if (query) {
        // Update the search query state to trigger API refetch
        setCurrentSearchQuery(query);
        console.log('🔍 Updated currentSearchQuery to:', query);
      }
    };

    const handleAIAutoshop = () => {
      if (isFullScreenMode) {
        // Show product display with general recommendations
        setCurrentSearchQuery('recommended products');
        setShowProductDisplay(true);

        // AutoShop activated - no popup needed
      }
    };

    // Add event listeners
    window.addEventListener('voiceStatus', handleVoiceStatus as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    window.addEventListener('daswosSearch', handleDaswosSearch as EventListener);

    return () => {
      window.removeEventListener('voiceStatus', handleVoiceStatus as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
      window.removeEventListener('daswosSearch', handleDaswosSearch as EventListener);
    };
  }, [isFullScreenMode, toast]);

  return (
    <div className={className}>
      {/* Show full robot interface when active */}
      {isRobotActive && (
        <>
          {/* Prominent Guess Counter - Only show when actively playing the guessing game */}
          {isFullScreenMode && (gameData || localGuessCount > 0) && (isGuessMode || showGuessResult) && (
            <div className="fixed top-16 right-4 z-[70] bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-lg">
              <div className="text-center">
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                  🎯 Guessing Game {gameData?.tablesNotReady && '(Demo)'}
                </div>
                {(() => {
                  // Use gameData if available, otherwise use local counters
                  const totalGuesses = gameData?.totalGuesses ?? localGuessCount;
                  const successfulGuesses = gameData?.successfulGuesses ?? localSuccessCount;
                  const freeGuessesRemaining = gameData?.freeGuessesRemaining ?? Math.max(0, 20 - localGuessCount);
                  const coinsEarned = gameData?.coinsEarned ?? (localSuccessCount * 20); // 20 coins per success

                  return (
                    <>
                      <div className={`text-2xl font-bold ${
                        freeGuessesRemaining <= 5
                          ? 'text-red-600 dark:text-red-400'
                          : freeGuessesRemaining <= 10
                          ? 'text-orange-600 dark:text-orange-400'
                          : 'text-blue-600 dark:text-blue-400'
                      }`}>
                        {freeGuessesRemaining}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Free guesses left
                      </div>

                      {/* Debug info */}
                      <div className="text-[8px] text-gray-400 mt-1">
                        Local: {localGuessCount} | API: {gameData?.totalGuesses ?? 'N/A'}
                      </div>

                      {/* Warning messages */}
                      {freeGuessesRemaining <= 5 && freeGuessesRemaining > 0 && (
                        <div className="text-xs text-red-600 dark:text-red-400 mt-1 font-medium">
                          ⚠️ Running low!
                        </div>
                      )}

                      {freeGuessesRemaining === 0 && (
                        <div className="text-xs text-red-600 dark:text-red-400 mt-1 font-medium">
                          {gameData?.isLoggedIn
                            ? "💰 Buy coins or connect wallet"
                            : "🔑 Sign up for more guesses"
                          }
                        </div>
                      )}

                      {/* Success stats */}
                      {successfulGuesses > 0 && (
                        <div className="text-xs text-green-600 dark:text-green-400 mt-2">
                          ✅ {successfulGuesses} successful today
                        </div>
                      )}

                      {coinsEarned > 0 && (
                        <div className="text-xs text-yellow-600 dark:text-yellow-400">
                          🪙 {coinsEarned} coins earned
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Robot Animation Layer - Only show when lights are on during guessing game */}
          <RobotAnimation
            ref={robotAnimationRef}
            onRobotStateChange={handleRobotStateChange}
            isVisible={isRobotActive && !isProductPopupOpen && (
              // Only show robot when user is actively playing the guessing game AND lights are on, OR when showing guess results
              (isGuessMode && lightsOn) || showGuessResult
            )}
            isGlidingAway={isRobotGlidingAway}
            isFullScreenMode={isFullScreenMode}
            showGuessResult={showGuessResult}
          />

          {/* Robot Controls - hide when gliding away or when controls are closed */}
          {!isRobotGlidingAway && showRobotControls && (
            <RobotControls
              robotState={robotState}
              robotScale={robotScale}
              onStateChange={handleStateChange}
              onScaleChange={handleScaleChange}
              onCenter={handleCenter}
              onRoll={handleRoll}
              onClose={handleCloseRobotControls}
              onFullScreenToggle={handleFullScreenToggle}
              isFullScreenMode={isFullScreenMode}
              onGoAway={handleGoAway}
              gameStatus={gameData}
              localGuessCount={localGuessCount}
              localSuccessCount={localSuccessCount}
            />
          )}

          {/* Search Bar - show when search is activated */}
          {showSearchBar && !isRobotGlidingAway && (
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[70] w-96">
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg">
                <div className="flex items-center gap-2 mb-3">
                  <Search className="h-5 w-5 text-gray-500" />
                  <h3 className="text-lg font-semibold">Search Products & Information</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSearchBar(false)}
                    className="ml-auto h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Search for products or information..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={currentSearchQuery}
                    onChange={(e) => setCurrentSearchQuery(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        // Default to product search on Enter
                        setShowProductDisplay(true);
                        if (!isFullScreenMode) {
                          handleFullScreenToggle();
                        }
                      }
                    }}
                    autoFocus
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        setShowProductDisplay(true);
                        // Keep search bar open after search
                        if (!isFullScreenMode) {
                          handleFullScreenToggle();
                        }
                      }}
                      className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700"
                    >
                      Search Products
                    </Button>
                    <Button
                      onClick={() => {
                        handleInformationSearch(currentSearchQuery);
                        // Keep search bar open after search
                      }}
                      variant="outline"
                      className="flex-1 px-4 py-2"
                    >
                      Search Information
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Information Search Popup */}
          {showInformationPopup && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[80] flex items-center justify-center p-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-xl font-semibold">Information Search Results</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowInformationPopup(false)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-4 overflow-y-auto max-h-[60vh]">
                  {informationResults.length > 0 ? (
                    <div className="space-y-4">
                      {informationResults.map((result, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <h3 className="font-semibold text-lg mb-2">{result.title}</h3>
                          <p className="text-gray-600 dark:text-gray-400 mb-2">{result.description}</p>
                          {result.url && (
                            <a
                              href={result.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                            >
                              Read more →
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">No information found for your search.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

        </>
      )}

      {/* Fullscreen Product Display - shows when robot is in full screen mode */}
      <FullscreenProductDisplay
        isVisible={isFullScreenMode}
        searchQuery={currentSearchQuery}
        onClose={handleCloseProductDisplay}
        onClearSearch={handleClearSearch}
        onProductPopupChange={setIsProductPopupOpen}
        onGoAway={handleGoAway}
        robotPosition={robotPosition}
        onRobotPositionChange={(position) => {
          if (position) {
            setRobotPosition(position);
            // Also update the robot animation position
            if (robotAnimationRef.current) {
              // Use setPosition to avoid changing center position for guessing game
              robotAnimationRef.current.setPosition(position.x, position.y);
            }
          }
        }}
        lightsOn={lightsOn}
        onLightsChange={handleLightsChange}
        onGuessMode={handleGuessMode}
        isGuessMode={isGuessMode}
        userGuess={userGuess}
        lastRobotPosition={lastRobotPosition}
        showGuessResult={showGuessResult}
        onScreenClick={handleScreenClick}
      />

      {/* Guess Result Popup - Completely Transparent Background with Smart Positioning */}
      {showGuessResult && (
        <div className="fixed inset-0 z-[2000] pointer-events-none">
          <div
            className="absolute bg-transparent border-none rounded-lg p-4 max-w-xs pointer-events-auto"
            style={{
              // Smart positioning - only move if robot is near center
              top: (() => {
                if (!robotPosition) {
                  console.log('🎯 No robot position - centering result message');
                  return '50%';
                }

                const screenHeight = window.innerHeight || 800;
                const screenCenterY = screenHeight / 2;
                const robotDistanceFromCenterY = Math.abs(robotPosition.y - screenCenterY);

                console.log('🎯 Robot Y distance from center:', robotDistanceFromCenterY);

                // Only move if robot is within 200px of vertical center
                if (robotDistanceFromCenterY < 200) {
                  const newPos = robotPosition.y < screenCenterY ? '75%' : '25%';
                  console.log('🎯 Robot near center vertically - moving result message to:', newPos);
                  return newPos;
                }

                console.log('🎯 Robot not near center vertically - keeping result message centered');
                return '50%';
              })(),
              left: (() => {
                if (!robotPosition) {
                  return '50%';
                }

                const screenWidth = window.innerWidth || 1200;
                const screenCenterX = screenWidth / 2;
                const robotDistanceFromCenterX = Math.abs(robotPosition.x - screenCenterX);

                console.log('🎯 Robot X distance from center:', robotDistanceFromCenterX);

                // Only move if robot is within 250px of horizontal center
                if (robotDistanceFromCenterX < 250) {
                  const newPos = robotPosition.x < screenCenterX ? '75%' : '25%';
                  console.log('🎯 Robot near center horizontally - moving result message to:', newPos);
                  return newPos;
                }

                console.log('🎯 Robot not near center horizontally - keeping result message centered');
                return '50%';
              })(),
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div className="text-center">
              <div className="text-3xl mb-2">
                {guessWasCorrect ? '🎉' : '🎯'}
              </div>
              <h2 className="text-white text-lg font-bold mb-1 drop-shadow-lg">
                {guessWasCorrect ? 'Congratulations!' : 'Nice Try!'}
              </h2>
              <p className="text-white text-sm mb-2 drop-shadow-lg">
                {guessWasCorrect
                  ? `You guessed correctly! Daswos appeared within ${guessDistance}px of your guess!`
                  : `You were ${guessDistance}px away from where Daswos appeared.`
                }
              </p>
              <p className="text-white text-xs mb-3 drop-shadow-lg">
                {guessWasCorrect
                  ? 'You have excellent prediction skills! 🌟'
                  : 'Try again next time! The target zone is 100px radius.'
                }
              </p>
              {/* Show coin reward info if available */}
              {lastGuessResult?.coinsAwarded > 0 && (
                <p className="text-yellow-300 text-sm font-bold mb-2 drop-shadow-lg">
                  🪙 +{lastGuessResult.coinsAwarded} DasWos Coins Earned!
                </p>
              )}
              <button
                onClick={() => setShowGuessResult(false)}
                className="bg-white/30 hover:bg-white/40 text-white px-4 py-1 rounded text-sm transition-colors drop-shadow-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reward Modal for Successful Guesses */}
      {showRewardModal && lastGuessResult && (
        <div className="fixed inset-0 z-[3000] bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
            <div className="text-6xl mb-4">🎉</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Congratulations!
            </h2>
            <p className="text-lg text-gray-600 mb-4">
              {lastGuessResult.message}
            </p>

            {lastGuessResult.coinsAwarded > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <div className="text-3xl mb-2">🪙</div>
                <p className="text-lg font-bold text-yellow-800">
                  +{lastGuessResult.coinsAwarded} DasWos Coins
                </p>
                <p className="text-sm text-yellow-600">
                  {lastGuessResult.isAnonymous
                    ? "Use your coins immediately to purchase something!"
                    : "Added to your account balance"
                  }
                </p>
              </div>
            )}

            {gameData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-sm">
                <p className="text-blue-800">
                  <strong>Today's Stats:</strong> {gameData.successfulGuesses + 1} successful guesses,
                  {gameData.freeGuessesRemaining - 1} free guesses remaining
                </p>
              </div>
            )}

            {lastGuessResult.mustSpendImmediately && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                <p className="text-orange-800 text-sm">
                  ⚠️ As an anonymous user, you must spend your earned coins immediately!
                </p>
              </div>
            )}

            <button
              onClick={() => {
                setShowRewardModal(false);
                setLastGuessResult(null);
                // Ensure purchase modal is not shown after successful guess
                setShowPurchaseModal(false);
                console.log('🎉 Reward modal closed - user can continue playing');
              }}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Awesome!
            </button>
          </div>
        </div>
      )}

      {/* Purchase Options Modal for Out of Guesses */}
      {showPurchaseModal && (
        <div className="fixed inset-0 z-[3000] bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center">
            <div className="text-6xl mb-4">🎯</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Out of Free Guesses!
            </h2>
            <p className="text-gray-600 mb-6">
              You've used your 20 free guesses today. Here are your options to continue playing:
            </p>

            <div className="space-y-4 mb-6">
              {!gameData?.isLoggedIn ? (
                <>
                  {/* Guest User Options */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-800 mb-2">🔑 Sign Up (Recommended)</h3>
                    <p className="text-sm text-blue-700 mb-3">
                      Create an account to buy DasWos coins and continue playing immediately!
                    </p>
                    <button
                      onClick={() => {
                        setShowPurchaseModal(false);
                        // Navigate to signup - you'll need to implement this
                        window.location.href = '/signup';
                      }}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors w-full"
                    >
                      Sign Up Now
                    </button>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-2">⏰ Come Back Tomorrow</h3>
                    <p className="text-sm text-green-700">
                      Get 20 new free guesses every day at midnight!
                    </p>
                  </div>
                </>
              ) : (
                <>
                  {/* Logged In User Options */}
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-800 mb-2">💰 Buy DasWos Coins</h3>
                    <p className="text-sm text-purple-700 mb-3">
                      Purchase DasWos coins to continue playing. Each guess costs 0.50 coins ($0.50).
                    </p>
                    <button
                      onClick={() => {
                        setShowPurchaseModal(false);
                        // Prevent navigation if user just had a successful guess
                        if (lastGuessResult?.isSuccessful || showRewardModal) {
                          console.log('🚫 Preventing checkout navigation - user just had successful guess');
                          toast({
                            title: "Continue Playing!",
                            description: "You just had a successful guess! Keep playing to earn more coins.",
                            duration: 3000,
                          });
                          return;
                        }
                        // Navigate to coin purchase
                        console.log('💰 Navigating to checkout for coin purchase');
                        window.location.href = '/checkout?coins=true';
                      }}
                      className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors w-full"
                    >
                      Buy DasWos Coins
                    </button>
                  </div>

                  {!gameData?.hasWallet && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <h3 className="font-semibold text-orange-800 mb-2">🔗 Connect Wallet</h3>
                      <p className="text-sm text-orange-700 mb-3">
                        Connect your DasWos wallet to use existing coins for more guesses.
                      </p>
                      <button
                        onClick={() => {
                          setShowPurchaseModal(false);
                          // Navigate to wallet connection - you'll need to implement this
                          window.location.href = '/wallet';
                        }}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors w-full"
                      >
                        Connect Wallet
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>

            <button
              onClick={() => setShowPurchaseModal(false)}
              className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Maybe Later
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RobotOverlay;

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { useSessionCoins } from '@/contexts/session-coins-context';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { DasWosCoinIcon } from '@/components/daswos-coin-icon';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Trash2, CreditCard, Coins, Package } from 'lucide-react';

interface CartItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  imageUrl: string;
  quantity: number;
  source: string;
  description?: string;
}

const CheckoutPage: React.FC = () => {
  const { user } = useAuth();
  const { sessionCoins, spendSessionCoins } = useSessionCoins();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [paymentMethod, setPaymentMethod] = useState<'stripe' | 'coins' | 'combined'>('stripe');
  const [coinsToUse, setCoinsToUse] = useState(0);

  // Shipping address will be collected by Stripe Checkout

  // Payment details are handled by Stripe Checkout

  // Order tracking will be handled through user account

  // Fetch cart items
  const { data: cartItems = [], isLoading } = useQuery({
    queryKey: ['/api/user/cart'],
    queryFn: () => apiRequest('/api/user/cart'),
  });

  // Calculate totals
  const regularItems = cartItems.filter((item: CartItem) => item.source !== 'ai_shopper');
  const aiShopperItems = cartItems.filter((item: CartItem) => item.source === 'ai_shopper');

  // Calculate totals: prices are stored as dollars in the database
  const regularTotal = regularItems.reduce((sum: number, item: CartItem) => sum + (item.price * item.quantity), 0);
  // For DasWos coins: 1 dollar = 1 DasWos coin
  const aiShopperTotal = aiShopperItems.reduce((sum: number, item: CartItem) => sum + (item.price * item.quantity), 0);
  const grandTotal = regularTotal + aiShopperTotal;

  // Available coins (session coins for anonymous, account coins for logged users)
  const availableCoins = user ? (user.dasWosCoins || 0) : sessionCoins;

  // Check for coin discount from URL parameters
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const coinDiscount = urlParams.get('coinDiscount');
    if (coinDiscount && availableCoins > 0) {
      const discountAmount = parseInt(coinDiscount, 10);
      if (discountAmount > 0 && discountAmount <= availableCoins) {
        setCoinsToUse(discountAmount);
        setPaymentMethod('combined');
      }
    }
  }, [availableCoins]);
  const maxCoinsUsable = Math.min(availableCoins, grandTotal);

  // Calculate payment breakdown
  const remainingAfterCoins = Math.max(0, grandTotal - coinsToUse);

  // Stripe Checkout will handle address collection and validation

  const validatePaymentDetails = () => {
    // Stripe Checkout handles payment details collection
    return true;
  };

  const canProceedToPayment = () => {
    return true; // Stripe Checkout handles all validation
  };

  // Create order function
  const createOrder = async (items: CartItem[], paymentMethod: string, coinsUsed: number, usdAmount: number) => {
    try {
      const orderData = {
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          name: item.name
        })),
        shippingAddress: null, // Will be collected by Stripe or set later
        paymentMethod,
        totalAmount: items.reduce((sum: number, item: CartItem) => sum + (item.price * item.quantity), 0),
        coinsUsed,
        usdAmount
      };

      const orderResponse = await apiRequest('/api/orders/create', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      console.log('Order created successfully:', orderResponse);

      // Clear purchased items from cart
      for (const item of items) {
        await apiRequest(`/api/user/cart/item/${item.id}`, { method: 'DELETE' });
      }

      queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });

    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  };

  // Remove item mutation
  const removeItemMutation = useMutation({
    mutationFn: (itemId: number) => 
      apiRequest(`/api/user/cart/item/${itemId}`, { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });
      toast({
        title: "Item Removed",
        description: "Item has been removed from your cart.",
      });
    },
  });

  // Update quantity mutation
  const updateQuantityMutation = useMutation({
    mutationFn: ({ itemId, quantity }: { itemId: number; quantity: number }) =>
      apiRequest(`/api/user/cart/item/${itemId}`, {
        method: 'PUT',
        body: JSON.stringify({ quantity }),
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });
    },
  });

  // Process payment using Stripe Checkout
  const processPayment = async (items: CartItem[]) => {
    // Support both guest and authenticated users - Stripe will collect shipping

    const itemsTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const coinsForItems = Math.min(coinsToUse, itemsTotal);
    const usdForItems = itemsTotal - coinsForItems;

    // Don't spend coins here - they will be spent at the appropriate time for each payment method

    // Handle DasWos coins only payment
    if (paymentMethod === 'coins') {
      // Spend coins first for coins-only purchases
      if (!user) {
        if (!spendSessionCoins(coinsForItems)) {
          throw new Error('Insufficient session coins');
        }
      } else {
        // Spend account coins for logged-in users
        const response = await apiRequest('/api/user/spend-coins', {
          method: 'POST',
          body: JSON.stringify({
            amount: coinsForItems,
            description: `Purchase of ${items.length} item(s)`
          })
        });

        if (!response.success) {
          throw new Error('Failed to spend DasWos coins');
        }
      }

      // Create order directly for coins-only purchases
      await createOrder(items, 'daswos_coins', coinsForItems, 0);
      return;
    }

    // For Stripe payments (including combined), redirect to Stripe Checkout
    if (paymentMethod === 'stripe' || (paymentMethod === 'combined' && usdForItems > 0)) {
      try {
        const checkoutData = {
          userId: user?.id || null,
          cartItems: items.map(item => ({
            productId: item.productId,
            quantity: item.quantity
          })),
          coinsUsed: coinsForItems,
          paymentMethod
        };

        const response = await apiRequest('/api/stripe/create-checkout-session', {
          method: 'POST',
          body: JSON.stringify(checkoutData)
        });

        if (response.success && response.url) {
          // Spend coins BEFORE redirecting to Stripe for combined payments
          if (paymentMethod === 'combined' && coinsForItems > 0) {
            if (!user) {
              if (!spendSessionCoins(coinsForItems)) {
                throw new Error('Insufficient session coins');
              }
            } else {
              const coinResponse = await apiRequest('/api/user/spend-coins', {
                method: 'POST',
                body: JSON.stringify({
                  amount: coinsForItems,
                  description: `Partial payment for ${items.length} item(s)`
                })
              });

              if (!coinResponse.success) {
                throw new Error('Failed to spend DasWos coins');
              }
            }
          }

          // Redirect to Stripe Checkout
          window.location.href = response.url;
          return; // Don't continue with order creation here - webhook will handle it
        } else {
          throw new Error('Failed to create checkout session');
        }
      } catch (error) {
        console.error('Stripe checkout error:', error);
        throw new Error(`Payment processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Order creation is now handled by createOrder function or Stripe webhook
  };

  // Purchase individual item
  const purchaseIndividualItem = async (item: CartItem) => {
    try {
      await processPayment([item]);
      toast({
        title: "Purchase Successful! 🎉",
        description: `Successfully purchased ${item.name}`,
        duration: 5000,
      });
    } catch (error) {
      toast({
        title: "Purchase Failed",
        description: "There was an error processing your payment.",
        variant: "destructive",
      });
    }
  };

  // Purchase all items
  const purchaseAllItems = async () => {
    try {
      await processPayment(cartItems);
      toast({
        title: "Purchase Successful! 🎉",
        description: `Successfully purchased all ${cartItems.length} items`,
        duration: 5000,
      });
    } catch (error) {
      toast({
        title: "Purchase Failed",
        description: "There was an error processing your payment.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading checkout...</div>
      </div>
    );
  }

  if (cartItems.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <h2 className="text-2xl font-bold mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-4">Add some items to your cart to proceed with checkout.</p>
            <Button onClick={() => window.location.href = '/'}>
              Continue Shopping
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Support both guest and authenticated users - no sign-in requirement

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Checkout</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Cart Items and Forms */}
        <div className="lg:col-span-2 space-y-6">
          {/* Cart Items */}
          <Card>
            <CardHeader>
              <CardTitle>Your Items ({cartItems.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {cartItems.map((item: CartItem) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <img
                      src={item.imageUrl || '/placeholder-product.svg'}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded bg-gray-100"
                      onError={(e) => {
                        const img = e.target as HTMLImageElement;
                        if (img.src !== '/placeholder-product.svg') {
                          img.src = '/placeholder-product.svg';
                        }
                      }}
                    />
                    <div className="flex-1">
                      <h3 className="font-medium">{item.name}</h3>
                      <p className="text-sm text-gray-600">${item.price.toLocaleString()} each</p>
                      <p className="text-xs text-gray-500">Source: {item.source}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantityMutation.mutate({ 
                          itemId: item.id, 
                          quantity: Math.max(1, item.quantity - 1) 
                        })}
                        className="w-8 h-8 border rounded flex items-center justify-center"
                      >
                        -
                      </button>
                      <span className="w-8 text-center">{item.quantity}</span>
                      <button
                        onClick={() => updateQuantityMutation.mutate({ 
                          itemId: item.id, 
                          quantity: item.quantity + 1 
                        })}
                        className="w-8 h-8 border rounded flex items-center justify-center"
                      >
                        +
                      </button>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${(item.price * item.quantity).toLocaleString()}</p>
                      <div className="flex space-x-2 mt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => purchaseIndividualItem(item)}
                        >
                          Buy Just This Item
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeItemMutation.mutate(item.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Shipping Info */}
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <Package className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="font-medium text-blue-800">Shipping Address Collection</h3>
                </div>
                <p className="text-sm text-blue-700">
                  Your shipping address will be collected securely during the checkout process.
                  Stripe will handle address validation and ensure accurate delivery information.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Payment Info */}
          {(paymentMethod === 'stripe' || paymentMethod === 'combined') && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <CreditCard className="w-5 h-5 text-blue-600 mr-2" />
                    <h3 className="font-medium text-blue-800">Secure Stripe Checkout</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    You'll be redirected to Stripe's secure checkout page to enter your payment details.
                    Stripe handles all payment processing securely.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Payment Summary */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Payment Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Totals */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Regular Items:</span>
                  <span>${regularTotal.toFixed(2)}</span>
                </div>
                {aiShopperTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>AI Shopper Items:</span>
                    <span>${aiShopperTotal.toFixed(2)}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total:</span>
                  <span>${grandTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>DasWos Coins Equivalent:</span>
                  <span className="flex items-center">
                    <DasWosCoinIcon className="mr-1" size={12} />
                    {grandTotal.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Available Coins */}
              {availableCoins > 0 ? (
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <span>Available {user ? 'Account' : 'Session'} Coins:</span>
                    <span className="flex items-center font-medium">
                      <DasWosCoinIcon className="mr-1" size={12} />
                      {availableCoins}
                    </span>
                  </div>
                </div>
              ) : !user && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">💡 Earn DasWos Coins!</p>
                    <p className="text-xs">Play the robot guessing game to earn session coins and pay with them!</p>
                  </div>
                </div>
              )}

              {/* Payment Method Selection */}
              <div className="space-y-3">
                <h4 className="font-medium">Payment Method</h4>
                
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="stripe"
                      checked={paymentMethod === 'stripe'}
                      onChange={(e) => setPaymentMethod(e.target.value as any)}
                    />
                    <CreditCard className="w-4 h-4" />
                    <span>Credit/Debit Card (Stripe)</span>
                  </label>

                  {availableCoins > 0 && (
                    <>
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="coins"
                          checked={paymentMethod === 'coins'}
                          onChange={(e) => setPaymentMethod(e.target.value as any)}
                          disabled={availableCoins < grandTotal}
                        />
                        <Coins className="w-4 h-4" />
                        <span>DasWos Coins Only</span>
                        {availableCoins < grandTotal && (
                          <span className="text-xs text-red-500">(Insufficient coins)</span>
                        )}
                      </label>

                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="combined"
                          checked={paymentMethod === 'combined'}
                          onChange={(e) => setPaymentMethod(e.target.value as any)}
                        />
                        <div className="flex items-center">
                          <Coins className="w-4 h-4 mr-1" />
                          <CreditCard className="w-4 h-4" />
                        </div>
                        <span>Coins + Credit Card</span>
                      </label>
                    </>
                  )}
                </div>

                {/* Coins slider for combined payment */}
                {paymentMethod === 'combined' && availableCoins > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Use {coinsToUse} DasWos Coins
                    </label>
                    <input
                      type="range"
                      min="0"
                      max={maxCoinsUsable}
                      value={coinsToUse}
                      onChange={(e) => setCoinsToUse(parseInt(e.target.value))}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>0 coins</span>
                      <span>{maxCoinsUsable} coins</span>
                    </div>
                  </div>
                )}

                {/* Payment breakdown */}
                {(paymentMethod === 'combined' || paymentMethod === 'coins') && availableCoins > 0 && (
                  <div className="bg-blue-50 p-3 rounded-lg space-y-1 text-sm">
                    {paymentMethod === 'coins' && (
                      <div className="flex justify-between">
                        <span>DasWos Coins:</span>
                        <span>{grandTotal} coins</span>
                      </div>
                    )}
                    {paymentMethod === 'combined' && (
                      <>
                        <div className="flex justify-between">
                          <span>DasWos Coins:</span>
                          <span>{coinsToUse} coins</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Credit Card:</span>
                          <span>${remainingAfterCoins.toFixed(2)}</span>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Ready to checkout */}

              {/* Purchase Button */}
              <Button
                className="w-full"
                size="lg"
                onClick={purchaseAllItems}
                disabled={
                  (paymentMethod === 'coins' && availableCoins < grandTotal) ||
                  cartItems.length === 0
                }
              >
                {paymentMethod === 'stripe' && `Pay $${grandTotal.toFixed(2)}`}
                {paymentMethod === 'coins' && `Pay ${grandTotal} DasWos Coins`}
                {paymentMethod === 'combined' && `Pay ${coinsToUse} Coins + $${remainingAfterCoins.toFixed(2)}`}
              </Button>

              {/* Order Tracking for Authenticated Users */}
              {user && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">📦 Order Tracking</h4>
                  <p className="text-sm text-blue-700 mb-3">
                    After purchase, you can track your orders in your account
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => window.location.href = '/purchases'}
                  >
                    View My Orders
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;

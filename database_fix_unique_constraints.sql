-- =====================================================
-- DATABASE FIX: Ensure Unique Constraints on Users Table
-- =====================================================
-- This script fixes the issue where duplicate usernames and emails
-- can be created by ensuring proper unique constraints are in place
-- =====================================================

BEGIN;

-- Step 1: Check for existing duplicate usernames and emails
DO $$
DECLARE
    duplicate_usernames INTEGER;
    duplicate_emails INTEGER;
    rec RECORD;
BEGIN
    -- Check for duplicate usernames (case-insensitive)
    SELECT COUNT(*) INTO duplicate_usernames
    FROM (
        SELECT LOWER(username) as lower_username, COUNT(*) as count
        FROM users
        GROUP BY LOWER(username)
        HAVING COUNT(*) > 1
    ) duplicates;

    -- Check for duplicate emails (case-insensitive)
    SELECT COUNT(*) INTO duplicate_emails
    FROM (
        SELECT LOWER(email) as lower_email, COUNT(*) as count
        FROM users
        GROUP BY LOWER(email)
        HAVING COUNT(*) > 1
    ) duplicates;

    RAISE NOTICE '🔍 Found % duplicate username groups and % duplicate email groups', duplicate_usernames, duplicate_emails;

    -- If duplicates exist, show them
    IF duplicate_usernames > 0 THEN
        RAISE NOTICE '📋 Duplicate usernames:';
        FOR rec IN 
            SELECT LOWER(username) as lower_username, array_agg(id) as user_ids, array_agg(username) as usernames
            FROM users
            GROUP BY LOWER(username)
            HAVING COUNT(*) > 1
        LOOP
            RAISE NOTICE '  - Username: % (IDs: %, Actual usernames: %)', rec.lower_username, rec.user_ids, rec.usernames;
        END LOOP;
    END IF;

    IF duplicate_emails > 0 THEN
        RAISE NOTICE '📋 Duplicate emails:';
        FOR rec IN 
            SELECT LOWER(email) as lower_email, array_agg(id) as user_ids, array_agg(email) as emails
            FROM users
            GROUP BY LOWER(email)
            HAVING COUNT(*) > 1
        LOOP
            RAISE NOTICE '  - Email: % (IDs: %, Actual emails: %)', rec.lower_email, rec.user_ids, rec.emails;
        END LOOP;
    END IF;
END $$;

-- Step 2: Clean up duplicate usernames (keep the oldest account)
DO $$
DECLARE
    rec RECORD;
    oldest_id INTEGER;
    ids_to_delete INTEGER[];
BEGIN
    FOR rec IN 
        SELECT LOWER(username) as lower_username, array_agg(id ORDER BY created_at ASC) as user_ids
        FROM users
        GROUP BY LOWER(username)
        HAVING COUNT(*) > 1
    LOOP
        -- Keep the first (oldest) account, mark others for deletion
        oldest_id := rec.user_ids[1];
        ids_to_delete := rec.user_ids[2:];
        
        RAISE NOTICE '🧹 Cleaning duplicate username "%": keeping ID % (oldest), deleting IDs %', 
                     rec.lower_username, oldest_id, ids_to_delete;
        
        -- Delete the duplicate accounts
        DELETE FROM users WHERE id = ANY(ids_to_delete);
    END LOOP;
END $$;

-- Step 3: Clean up duplicate emails (keep the oldest account)
DO $$
DECLARE
    rec RECORD;
    oldest_id INTEGER;
    ids_to_delete INTEGER[];
BEGIN
    FOR rec IN 
        SELECT LOWER(email) as lower_email, array_agg(id ORDER BY created_at ASC) as user_ids
        FROM users
        GROUP BY LOWER(email)
        HAVING COUNT(*) > 1
    LOOP
        -- Keep the first (oldest) account, mark others for deletion
        oldest_id := rec.user_ids[1];
        ids_to_delete := rec.user_ids[2:];
        
        RAISE NOTICE '🧹 Cleaning duplicate email "%": keeping ID % (oldest), deleting IDs %', 
                     rec.lower_email, oldest_id, ids_to_delete;
        
        -- Delete the duplicate accounts
        DELETE FROM users WHERE id = ANY(ids_to_delete);
    END LOOP;
END $$;

-- Step 4: Drop existing indexes if they exist (to recreate them properly)
DROP INDEX IF EXISTS idx_users_username_unique_ci;
DROP INDEX IF EXISTS idx_users_email_unique_ci;

-- Step 5: Create case-insensitive unique indexes
CREATE UNIQUE INDEX idx_users_username_unique_ci ON users (LOWER(username));
CREATE UNIQUE INDEX idx_users_email_unique_ci ON users (LOWER(email));

-- Step 6: Verify the constraints are working
DO $$
DECLARE
    username_constraint_exists BOOLEAN;
    email_constraint_exists BOOLEAN;
BEGIN
    -- Check if the unique indexes exist
    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_users_username_unique_ci'
    ) INTO username_constraint_exists;

    SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_users_email_unique_ci'
    ) INTO email_constraint_exists;

    IF username_constraint_exists THEN
        RAISE NOTICE '✅ Username unique constraint (case-insensitive) is active';
    ELSE
        RAISE WARNING '❌ Username unique constraint failed to create';
    END IF;

    IF email_constraint_exists THEN
        RAISE NOTICE '✅ Email unique constraint (case-insensitive) is active';
    ELSE
        RAISE WARNING '❌ Email unique constraint failed to create';
    END IF;
END $$;

-- Step 7: Test the constraints by attempting to create duplicates (this should fail)
DO $$
BEGIN
    -- This should fail if constraints are working
    BEGIN
        INSERT INTO users (username, email, full_name, password) 
        VALUES ('TEST_DUPLICATE_USER', '<EMAIL>', 'Test User', 'password123');
        
        INSERT INTO users (username, email, full_name, password) 
        VALUES ('test_duplicate_user', '<EMAIL>', 'Test User 2', 'password456');
        
        RAISE WARNING '❌ CONSTRAINT TEST FAILED: Duplicate users were created!';
        
        -- Clean up test data
        DELETE FROM users WHERE username IN ('TEST_DUPLICATE_USER', 'test_duplicate_user');
        
    EXCEPTION WHEN unique_violation THEN
        RAISE NOTICE '✅ CONSTRAINT TEST PASSED: Duplicate creation properly blocked';
        
        -- Clean up any test data that might have been created
        DELETE FROM users WHERE username IN ('TEST_DUPLICATE_USER', 'test_duplicate_user');
    END;
END $$;

COMMIT;

RAISE NOTICE '🎉 Database unique constraints fix completed successfully!';
RAISE NOTICE '📝 Username and email duplicates have been cleaned up';
RAISE NOTICE '🔒 Case-insensitive unique constraints are now active';
RAISE NOTICE '⚠️  Future attempts to create duplicate usernames/emails will be blocked';

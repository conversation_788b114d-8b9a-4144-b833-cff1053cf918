-- SQL script to add wallet products for testing search functionality
-- This will help test the improved search precision

INSERT INTO products (
    title,
    description,
    price,
    image_url,
    seller_id,
    seller_name,
    seller_verified,
    seller_type,
    trust_score,
    tags,
    shipping,
    category_id,
    status,
    quantity,
    created_at,
    updated_at
) VALUES
-- Leather Wallet
(
    'Premium Leather Wallet',
    'Handcrafted genuine leather wallet with multiple card slots and bill compartment. Perfect for everyday use.',
    4999, -- $49.99
    'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
    1, -- admin user
    'admin',
    true,
    'merchant',
    85,
    '{"wallet", "leather", "accessories", "men", "fashion"}',
    'standard',
    2, -- Clothing category
    'active',
    25,
    NOW(),
    NOW()
),
-- Minimalist Wallet
(
    'Minimalist RFID Wallet',
    'Slim minimalist wallet with RFID blocking technology. Holds up to 8 cards and cash.',
    2999, -- $29.99
    'https://images.unsplash.com/photo-1627123424574-724758594e93?w=400&h=400&fit=crop',
    1,
    'admin',
    true,
    'merchant',
    85,
    '{"wallet", "minimalist", "rfid", "slim", "modern"}',
    'standard',
    2,
    'active',
    30,
    NOW(),
    NOW()
),
-- Women's Wallet
(
    'Designer Women''s Wallet',
    'Elegant women''s wallet with zipper closure, multiple compartments, and stylish design.',
    3499, -- $34.99
    'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400&h=400&fit=crop',
    1,
    'admin',
    true,
    'merchant',
    85,
    '{"wallet", "women", "designer", "zipper", "fashion"}',
    'standard',
    2,
    'active',
    20,
    NOW(),
    NOW()
),
-- Digital Wallet
(
    'Smart Digital Wallet',
    'High-tech digital wallet with Bluetooth tracking and smartphone integration.',
    7999, -- $79.99
    'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
    1,
    'admin',
    true,
    'merchant',
    85,
    '{"wallet", "digital", "smart", "bluetooth", "technology"}',
    'standard',
    1, -- Electronics category
    'active',
    15,
    NOW(),
    NOW()
),
-- Vintage Wallet
(
    'Vintage Leather Wallet',
    'Classic vintage-style leather wallet with aged finish and traditional craftsmanship.',
    5999, -- $59.99
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
    1,
    'admin',
    true,
    'merchant',
    85,
    '{"wallet", "vintage", "leather", "classic", "handmade"}',
    'standard',
    2,
    'active',
    18,
    NOW(),
    NOW()
);

-- Success message
SELECT 'WALLET PRODUCTS ADDED SUCCESSFULLY!' as status,
       COUNT(*) as wallet_products_count
FROM products 
WHERE 'wallet' = ANY(tags);

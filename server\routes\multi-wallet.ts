import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { WALLET_DATABASE_CONFIG } from '../../wallet_database_config.js';
import { requireAuth } from '../middleware/auth.js';
import { db } from '../db/index.js';
import { users } from '../../shared/schema.js';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcrypt';

const router = express.Router();

// Initialize Supabase client for wallet database
const walletSupabase = createClient(
  WALLET_DATABASE_CONFIG.SUPABASE_URL,
  WALLET_DATABASE_CONFIG.SUPABASE_KEY
);

/**
 * GET /api/multi-wallet/user/:user_id
 * Get all wallets for a user
 */
router.get('/user/:user_id', requireAuth, async (req, res) => {
  try {
    const { user_id } = req.params;
    const userId = parseInt(user_id);

    // Get user's wallet information from main database
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Get wallet details from wallet database
    const { data: walletConnections, error } = await walletSupabase
      .from('wallet_connections')
      .select(`
        *,
        wallets:wallet_id (
          wallet_id,
          wallet_type,
          wallet_name,
          wallet_description,
          is_children_wallet,
          parent_wallet_id,
          age_restriction,
          spending_limit_daily,
          spending_limit_monthly,
          allowed_categories,
          restricted_features,
          created_at,
          is_active
        )
      `)
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .eq('is_active', true)
      .order('wallet_order', { ascending: true });

    if (error) {
      console.error('Error fetching user wallets:', error);
      return res.status(500).json({
        error: 'Failed to fetch user wallets',
        details: error.message
      });
    }

    res.json({
      success: true,
      user: {
        id: userData.id,
        username: userData.username,
        primaryWalletId: userData.primaryWalletId,
        activeWalletId: userData.activeWalletId,
        walletIds: userData.walletIds || [],
        walletNicknames: userData.walletNicknames || {}
      },
      wallets: walletConnections || [],
      walletCount: walletConnections?.length || 0,
      maxWallets: 5
    });

  } catch (error) {
    console.error('Error in GET /api/multi-wallet/user/:user_id:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/multi-wallet/create
 * Create a new wallet for a user
 */
router.post('/create', requireAuth, async (req, res) => {
  try {
    const {
      userId,
      username,
      walletType = 'standard',
      walletName,
      walletDescription,
      password,
      // Children's wallet specific fields
      isChildrenWallet = false,
      parentWalletId,
      ageRestriction,
      spendingLimitDaily = 0,
      spendingLimitMonthly = 0,
      allowedCategories = [],
      restrictedFeatures = []
    } = req.body;

    // Validate required fields
    if (!userId || !username || !password) {
      return res.status(400).json({
        error: 'Missing required fields: userId, username, password'
      });
    }

    // Check if user exists
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check wallet limit (max 5 wallets per user)
    const currentWalletCount = userData.walletIds?.length || 0;
    if (currentWalletCount >= 5) {
      return res.status(400).json({
        error: 'Maximum wallet limit reached',
        message: 'Users can have a maximum of 5 wallets'
      });
    }

    // Validate children's wallet requirements
    if (isChildrenWallet && !parentWalletId) {
      return res.status(400).json({
        error: 'Parent wallet ID is required for children\'s wallets'
      });
    }

    // Generate wallet ID based on type
    let walletIdFunction;
    switch (walletType) {
      case 'children':
        walletIdFunction = 'generate_children_wallet_id';
        break;
      case 'business':
        walletIdFunction = 'generate_business_wallet_id';
        break;
      default:
        walletIdFunction = 'generate_standard_wallet_id';
    }

    // Generate unique wallet ID
    const { data: walletIdResult, error: walletIdError } = await walletSupabase
      .rpc(walletIdFunction);

    if (walletIdError || !walletIdResult) {
      console.error('Error generating wallet ID:', walletIdError);
      return res.status(500).json({
        error: 'Failed to generate wallet ID',
        details: walletIdError?.message
      });
    }

    const newWalletId = walletIdResult;

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Set default restricted features for children's wallets
    const defaultRestrictedFeatures = isChildrenWallet
      ? ['opensphere', 'gambling', 'adult_content', ...restrictedFeatures]
      : restrictedFeatures;

    // Create wallet in wallet database
    const { data: newWallet, error: walletError } = await walletSupabase
      .from('wallets')
      .insert({
        wallet_id: newWalletId,
        password_hash: passwordHash,
        wallet_type: walletType,
        wallet_name: walletName,
        wallet_description: walletDescription,
        is_children_wallet: isChildrenWallet,
        parent_wallet_id: parentWalletId,
        age_restriction: ageRestriction,
        spending_limit_daily: spendingLimitDaily,
        spending_limit_monthly: spendingLimitMonthly,
        allowed_categories: allowedCategories,
        restricted_features: defaultRestrictedFeatures,
        creation_ip: req.ip
      })
      .select()
      .single();

    if (walletError) {
      console.error('Error creating wallet:', walletError);
      return res.status(500).json({
        error: 'Failed to create wallet',
        details: walletError.message
      });
    }

    // Determine wallet order (next available slot)
    const nextWalletOrder = currentWalletCount + 1;

    // Create wallet connection
    const { data: connection, error: connectionError } = await walletSupabase
      .from('wallet_connections')
      .insert({
        wallet_id: newWallet.id,
        database_name: 'daswos-18',
        user_id: userId,
        username: username,
        wallet_order: nextWalletOrder,
        wallet_nickname: walletName,
        is_primary: currentWalletCount === 0, // First wallet is primary
        is_active: true
      })
      .select()
      .single();

    if (connectionError) {
      console.error('Error creating wallet connection:', connectionError);
      // Clean up the wallet if connection failed
      await walletSupabase.from('wallets').delete().eq('id', newWallet.id);
      return res.status(500).json({
        error: 'Failed to create wallet connection',
        details: connectionError.message
      });
    }

    // Update user's wallet information in main database
    const updatedWalletIds = [...(userData.walletIds || []), newWalletId];
    const updatedWalletNicknames = {
      ...(userData.walletNicknames || {}),
      [newWalletId]: walletName || `Wallet ${nextWalletOrder}`
    };

    await db.update(users)
      .set({
        walletIds: updatedWalletIds,
        walletNicknames: updatedWalletNicknames,
        primaryWalletId: userData.primaryWalletId || newWalletId, // Set as primary if first wallet
        activeWalletId: userData.activeWalletId || newWalletId // Set as active if first wallet
      })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Wallet created successfully',
      wallet: {
        id: newWallet.id,
        walletId: newWalletId,
        walletType: walletType,
        walletName: walletName,
        isChildrenWallet: isChildrenWallet,
        walletOrder: nextWalletOrder,
        isPrimary: currentWalletCount === 0
      },
      connection: connection
    });

  } catch (error) {
    console.error('Error in POST /api/multi-wallet/create:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/multi-wallet/set-active
 * Set the active wallet for a user
 */
router.put('/set-active', requireAuth, async (req, res) => {
  try {
    const { userId, walletId } = req.body;

    if (!userId || !walletId) {
      return res.status(400).json({
        error: 'Missing required fields: userId, walletId'
      });
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check if wallet belongs to user
    if (!userData.walletIds?.includes(walletId)) {
      return res.status(403).json({
        error: 'Wallet does not belong to user'
      });
    }

    // Update active wallet
    await db.update(users)
      .set({ activeWalletId: walletId })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Active wallet updated successfully',
      activeWalletId: walletId
    });

  } catch (error) {
    console.error('Error in PUT /api/multi-wallet/set-active:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/multi-wallet/set-primary
 * Set the primary wallet for a user
 */
router.put('/set-primary', requireAuth, async (req, res) => {
  try {
    const { userId, walletId } = req.body;

    if (!userId || !walletId) {
      return res.status(400).json({
        error: 'Missing required fields: userId, walletId'
      });
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check if wallet belongs to user
    if (!userData.walletIds?.includes(walletId)) {
      return res.status(403).json({
        error: 'Wallet does not belong to user'
      });
    }

    // Update primary wallet in main database
    await db.update(users)
      .set({ primaryWalletId: walletId })
      .where(eq(users.id, userId));

    // Update wallet connections to set new primary
    await walletSupabase
      .from('wallet_connections')
      .update({ is_primary: false })
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18');

    await walletSupabase
      .from('wallet_connections')
      .update({ is_primary: true })
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .in('wallet_id', [walletId]);

    res.json({
      success: true,
      message: 'Primary wallet updated successfully',
      primaryWalletId: walletId
    });

  } catch (error) {
    console.error('Error in PUT /api/multi-wallet/set-primary:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/multi-wallet/delete
 * Delete a wallet (cannot delete primary wallet)
 */
router.delete('/delete', requireAuth, async (req, res) => {
  try {
    const { userId, walletId } = req.body;

    if (!userId || !walletId) {
      return res.status(400).json({
        error: 'Missing required fields: userId, walletId'
      });
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check if wallet belongs to user
    if (!userData.walletIds?.includes(walletId)) {
      return res.status(403).json({
        error: 'Wallet does not belong to user'
      });
    }

    // Cannot delete primary wallet
    if (userData.primaryWalletId === walletId) {
      return res.status(400).json({
        error: 'Cannot delete primary wallet',
        message: 'Please set another wallet as primary before deleting this wallet'
      });
    }

    // Get wallet details to check if it's a parent wallet
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .single();

    if (walletError) {
      return res.status(404).json({ error: 'Wallet not found' });
    }

    // Check if this wallet has children wallets
    const { data: childrenWallets, error: childrenError } = await walletSupabase
      .from('wallets')
      .select('wallet_id')
      .eq('parent_wallet_id', wallet.id);

    if (childrenError) {
      console.error('Error checking children wallets:', childrenError);
    }

    if (childrenWallets && childrenWallets.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete wallet with children wallets',
        message: 'Please delete or reassign children wallets first',
        childrenWallets: childrenWallets.map(w => w.wallet_id)
      });
    }

    // Delete wallet connection
    const { error: connectionDeleteError } = await walletSupabase
      .from('wallet_connections')
      .delete()
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .in('wallet_id', [walletId]);

    if (connectionDeleteError) {
      console.error('Error deleting wallet connection:', connectionDeleteError);
      return res.status(500).json({
        error: 'Failed to delete wallet connection',
        details: connectionDeleteError.message
      });
    }

    // Delete wallet
    const { error: walletDeleteError } = await walletSupabase
      .from('wallets')
      .delete()
      .eq('id', wallet.id);

    if (walletDeleteError) {
      console.error('Error deleting wallet:', walletDeleteError);
      return res.status(500).json({
        error: 'Failed to delete wallet',
        details: walletDeleteError.message
      });
    }

    // Update user's wallet information in main database
    const updatedWalletIds = userData.walletIds?.filter(id => id !== walletId) || [];
    const updatedWalletNicknames = { ...(userData.walletNicknames || {}) };
    delete updatedWalletNicknames[walletId];

    // If active wallet was deleted, set to primary
    const newActiveWalletId = userData.activeWalletId === walletId
      ? userData.primaryWalletId
      : userData.activeWalletId;

    await db.update(users)
      .set({
        walletIds: updatedWalletIds,
        walletNicknames: updatedWalletNicknames,
        activeWalletId: newActiveWalletId
      })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Wallet deleted successfully',
      deletedWalletId: walletId,
      remainingWallets: updatedWalletIds.length
    });

  } catch (error) {
    console.error('Error in DELETE /api/multi-wallet/delete:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/multi-wallet/check-restrictions/:wallet_id
 * Check if a wallet has restrictions for certain actions
 */
router.get('/check-restrictions/:wallet_id', requireAuth, async (req, res) => {
  try {
    const { wallet_id } = req.params;
    const { action, category, amount } = req.query;

    // Get wallet details
    const { data: wallet, error } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', wallet_id)
      .single();

    if (error || !wallet) {
      return res.status(404).json({ error: 'Wallet not found' });
    }

    const restrictions = {
      allowed: true,
      reasons: [] as string[],
      walletType: wallet.wallet_type,
      isChildrenWallet: wallet.is_children_wallet
    };

    // Check children's wallet restrictions
    if (wallet.is_children_wallet) {
      // Check restricted features
      if (action && wallet.restricted_features?.includes(action)) {
        restrictions.allowed = false;
        restrictions.reasons.push(`Action '${action}' is restricted for children's wallets`);
      }

      // Check category restrictions
      if (category && wallet.allowed_categories?.length > 0 && !wallet.allowed_categories.includes(category)) {
        restrictions.allowed = false;
        restrictions.reasons.push(`Category '${category}' is not allowed for this children's wallet`);
      }

      // Check spending limits
      if (amount) {
        const spendingAmount = parseInt(amount as string);

        if (wallet.spending_limit_daily > 0 && spendingAmount > wallet.spending_limit_daily) {
          restrictions.allowed = false;
          restrictions.reasons.push(`Amount exceeds daily spending limit of $${(wallet.spending_limit_daily / 100).toFixed(2)}`);
        }

        // TODO: Check actual daily/monthly spending against limits
        // This would require querying the spending tracking table
      }
    }

    res.json({
      success: true,
      walletId: wallet_id,
      restrictions
    });

  } catch (error) {
    console.error('Error in GET /api/multi-wallet/check-restrictions:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;

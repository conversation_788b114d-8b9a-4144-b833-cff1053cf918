-- Fix products to appear in SafeSphere by setting proper identity verification
-- This will make all existing products appear in SafeSphere searches

UPDATE products 
SET 
  identity_verified = true,
  identity_verification_status = 'approved',
  trust_score = CASE 
    WHEN trust_score < 70 THEN 75  -- Ensure minimum trust score for SafeSphere
    ELSE trust_score 
  END
WHERE identity_verified = false OR identity_verification_status = 'none';

-- Verify the update
SELECT 
  id, 
  title, 
  trust_score, 
  identity_verified, 
  identity_verification_status 
FROM products 
ORDER BY id;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image URL Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-image { 
            width: 200px; 
            height: 200px; 
            border: 1px solid #ccc; 
            margin: 10px; 
            object-fit: cover;
        }
        .error { color: red; }
        .success { color: green; }
        .test-container { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 20px; 
        }
        .test-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            max-width: 250px;
        }
    </style>
</head>
<body>
    <h1>Image URL Test</h1>
    <p>Testing various image URLs to see which ones work:</p>
    
    <div class="test-container">
        <!-- Placeholder SVG -->
        <div class="test-item">
            <h3>Placeholder SVG</h3>
            <img src="/placeholder-product.svg" alt="Placeholder" class="test-image" 
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
            <p class="status">Loading...</p>
        </div>

        <!-- Sample Unsplash URLs from database -->
        <div class="test-item">
            <h3>Unsplash - Headphones</h3>
            <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop" alt="Headphones" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
            <p class="status">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Running Shoes</h3>
            <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop" alt="Running Shoes" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
            <p class="status">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Smart Watch</h3>
            <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop" alt="Smart Watch" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
            <p class="status">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Wallet</h3>
            <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop" alt="Wallet" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'">
            <p class="status">Loading...</p>
        </div>
    </div>

    <script>
        // Test fetch API as well
        async function testImageUrls() {
            const urls = [
                '/placeholder-product.svg',
                'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
                'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
                'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop'
            ];

            console.log('Testing image URLs with fetch...');
            
            for (const url of urls) {
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    console.log(`✅ ${url}: ${response.status} ${response.statusText}`);
                } catch (error) {
                    console.log(`❌ ${url}: ${error.message}`);
                }
            }
        }

        // Run tests after page loads
        window.addEventListener('load', () => {
            setTimeout(testImageUrls, 2000);
        });
    </script>
</body>
</html>

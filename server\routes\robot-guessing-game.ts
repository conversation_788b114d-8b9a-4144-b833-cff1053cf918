import { Request, Response, Express } from 'express';
import { eq, and, sql } from 'drizzle-orm';
import { robotGuessingGame, robotGuesses, users, dasWosCoinsTransactions } from '@shared/schema';
import { getUserFromSession } from '../auth';
import { Storage } from '../storage';

const DAILY_FREE_GUESSES = 20;
const EXTRA_GUESS_COST = 50; // 0.50 DasWos coins (50 cents)
const SUCCESS_REWARD = 2000; // 20 DasWos coins (2000 cents)
const SUCCESS_THRESHOLD = 100; // pixels - how close the guess needs to be

export function setupRobotGuessingGameRoutes(app: Express, storage: Storage) {
  
  // Get today's game status for user
  app.get('/api/robot-guessing-game/status', async (req: Request, res: Response) => {
    try {
      const user = await getUserFromSession(req, storage);
      const sessionId = req.sessionID;
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      // Check if database tables exist
      if (!storage.db || !storage.db.select) {
        console.log('⚠️ Database tables not yet created, returning default game status');
        return res.json({
          gameId: null,
          totalGuesses: 0,
          successfulGuesses: 0,
          coinsEarned: 0,
          freeGuessesRemaining: DAILY_FREE_GUESSES,
          canGuessForFree: true,
          extraGuessCost: EXTRA_GUESS_COST,
          successReward: SUCCESS_REWARD,
          needsWallet: false,
          isLoggedIn: !!user,
          hasWallet: !!(user?.primaryWalletId || (user?.walletIds && user.walletIds.length > 0)),
          tablesNotReady: true
        });
      }

      // Get or create today's game record
      let gameRecord = await storage.db.select()
        .from(robotGuessingGame)
        .where(
          user
            ? and(eq(robotGuessingGame.userId, user.id), eq(robotGuessingGame.gameDate, today))
            : and(eq(robotGuessingGame.sessionId, sessionId), eq(robotGuessingGame.gameDate, today))
        )
        .limit(1);

      if (gameRecord.length === 0) {
        // Create new game record for today
        const newGame = await storage.db.insert(robotGuessingGame)
          .values({
            userId: user?.id || null,
            sessionId,
            gameDate: today,
            totalGuesses: 0,
            successfulGuesses: 0,
            coinsEarned: 0,
          })
          .returning();
        
        gameRecord = newGame;
      }

      const game = gameRecord[0];
      const freeGuessesRemaining = Math.max(0, DAILY_FREE_GUESSES - game.totalGuesses);
      const canGuessForFree = freeGuessesRemaining > 0;
      const needsWallet = !canGuessForFree && !(user?.primaryWalletId || (user?.walletIds && user.walletIds.length > 0));

      res.json({
        gameId: game.id,
        totalGuesses: game.totalGuesses,
        successfulGuesses: game.successfulGuesses,
        coinsEarned: game.coinsEarned,
        freeGuessesRemaining,
        canGuessForFree,
        extraGuessCost: EXTRA_GUESS_COST,
        successReward: SUCCESS_REWARD,
        needsWallet,
        isLoggedIn: !!user,
        hasWallet: !!(user?.primaryWalletId || (user?.walletIds && user.walletIds.length > 0)),
      });

    } catch (error) {
      console.error('Error getting game status:', error);
      res.status(500).json({ error: 'Failed to get game status' });
    }
  });

  // Submit a guess
  app.post('/api/robot-guessing-game/guess', async (req: Request, res: Response) => {
    try {
      const user = await getUserFromSession(req, storage);
      const sessionId = req.sessionID;
      const { guessX, guessY, actualX, actualY, gameId } = req.body;

      if (!guessX || !guessY || !actualX || !actualY) {
        return res.status(400).json({ error: 'Missing required guess data' });
      }

      // Check if database tables exist
      if (!storage.db || !storage.db.select) {
        console.log('⚠️ Database tables not yet created, simulating guess result');
        const distance = Math.sqrt(Math.pow(guessX - actualX, 2) + Math.pow(guessY - actualY, 2));
        const isSuccessful = distance <= SUCCESS_THRESHOLD;

        return res.json({
          success: true,
          isSuccessful,
          distance: Math.round(distance),
          coinsAwarded: isSuccessful ? SUCCESS_REWARD : 0,
          costPaid: 0,
          isAnonymous: !user,
          mustSpendImmediately: !user && isSuccessful,
          message: isSuccessful
            ? `Congratulations! You found Daswos and earned ${SUCCESS_REWARD / 100} DasWos coins! (Demo mode - tables not ready)`
            : `Close! You were ${Math.round(distance)} pixels away. Try again! (Demo mode)`,
          tablesNotReady: true
        });
      }

      // Get game record
      const gameRecord = await storage.db.select()
        .from(robotGuessingGame)
        .where(eq(robotGuessingGame.id, gameId))
        .limit(1);

      if (gameRecord.length === 0) {
        return res.status(404).json({ error: 'Game not found' });
      }

      const game = gameRecord[0];
      const freeGuessesRemaining = Math.max(0, DAILY_FREE_GUESSES - game.totalGuesses);
      const isExtraGuess = freeGuessesRemaining === 0;
      let costPaid = 0;

      // Check if this is an extra guess that requires payment
      if (isExtraGuess) {
        if (!user) {
          return res.status(401).json({
            error: 'Must be logged in to make extra guesses',
            needsLogin: true,
            freeGuessesUsed: true
          });
        }

        if (!user.primaryWalletId && (!user.walletIds || user.walletIds.length === 0)) {
          return res.status(400).json({
            error: 'Wallet connection or DasWos coin purchase required for extra guesses',
            needsWallet: true,
            needsCoins: true,
            freeGuessesUsed: true
          });
        }

        // Check if user has enough coins
        if (user.dasWosCoinsBalance < EXTRA_GUESS_COST) {
          return res.status(400).json({
            error: 'Insufficient DasWos coins for extra guess',
            required: EXTRA_GUESS_COST,
            available: user.dasWosCoinsBalance,
            needsCoins: true,
            freeGuessesUsed: true
          });
        }

        // Deduct cost for extra guess
        await storage.db.update(users)
          .set({ 
            dasWosCoinsBalance: sql`${users.dasWosCoinsBalance} - ${EXTRA_GUESS_COST}`,
            updatedAt: new Date()
          })
          .where(eq(users.id, user.id));

        // Record the transaction
        await storage.db.insert(dasWosCoinsTransactions)
          .values({
            userId: user.id,
            amount: -EXTRA_GUESS_COST,
            type: 'spend',
            description: 'Robot guessing game extra guess',
            metadata: { gameId, guessNumber: game.totalGuesses + 1 }
          });

        costPaid = EXTRA_GUESS_COST;
      }

      // Calculate distance and success
      const distance = Math.sqrt(Math.pow(guessX - actualX, 2) + Math.pow(guessY - actualY, 2));
      const isSuccessful = distance <= SUCCESS_THRESHOLD;
      let coinsAwarded = 0;

      // Award coins for successful guess
      if (isSuccessful) {
        if (user) {
          // Logged in user gets coins added to account
          await storage.db.update(users)
            .set({ 
              dasWosCoinsBalance: sql`${users.dasWosCoinsBalance} + ${SUCCESS_REWARD}`,
              updatedAt: new Date()
            })
            .where(eq(users.id, user.id));

          // Record the transaction
          await storage.db.insert(dasWosCoinsTransactions)
            .values({
              userId: user.id,
              amount: SUCCESS_REWARD,
              type: 'reward',
              description: 'Robot guessing game success reward',
              metadata: { gameId, distance: Math.round(distance) }
            });

          coinsAwarded = SUCCESS_REWARD;
        } else {
          // Anonymous user gets coins but must spend immediately
          coinsAwarded = SUCCESS_REWARD;
        }
      }

      // Record the guess
      await storage.db.insert(robotGuesses)
        .values({
          gameId,
          guessX,
          guessY,
          actualX,
          actualY,
          distance: Math.round(distance),
          isSuccessful,
          coinsAwarded,
          costPaid,
        });

      // Update game record
      await storage.db.update(robotGuessingGame)
        .set({
          totalGuesses: sql`${robotGuessingGame.totalGuesses} + 1`,
          successfulGuesses: isSuccessful ? sql`${robotGuessingGame.successfulGuesses} + 1` : robotGuessingGame.successfulGuesses,
          coinsEarned: sql`${robotGuessingGame.coinsEarned} + ${coinsAwarded}`,
          lastGuessAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(robotGuessingGame.id, gameId));

      res.json({
        success: true,
        isSuccessful,
        distance: Math.round(distance),
        coinsAwarded,
        costPaid,
        isAnonymous: !user,
        mustSpendImmediately: !user && isSuccessful,
        message: isSuccessful 
          ? `Congratulations! You found Daswos and earned ${coinsAwarded / 100} DasWos coins!`
          : `Close! You were ${Math.round(distance)} pixels away. Try again!`
      });

    } catch (error) {
      console.error('Error processing guess:', error);
      res.status(500).json({ error: 'Failed to process guess' });
    }
  });

  // Get guess history for today
  app.get('/api/robot-guessing-game/history', async (req: Request, res: Response) => {
    try {
      const user = await getUserFromSession(req, storage);
      const sessionId = req.sessionID;
      const today = new Date().toISOString().split('T')[0];

      // Get today's game record
      const gameRecord = await storage.db.select()
        .from(robotGuessingGame)
        .where(
          user 
            ? and(eq(robotGuessingGame.userId, user.id), eq(robotGuessingGame.gameDate, today))
            : and(eq(robotGuessingGame.sessionId, sessionId), eq(robotGuessingGame.gameDate, today))
        )
        .limit(1);

      if (gameRecord.length === 0) {
        return res.json({ guesses: [] });
      }

      // Get all guesses for today's game
      const guesses = await storage.db.select()
        .from(robotGuesses)
        .where(eq(robotGuesses.gameId, gameRecord[0].id))
        .orderBy(robotGuesses.createdAt);

      res.json({ guesses });

    } catch (error) {
      console.error('Error getting guess history:', error);
      res.status(500).json({ error: 'Failed to get guess history' });
    }
  });

  // Handle session coin spending for anonymous users
  app.post('/api/session-coins/spend', async (req: Request, res: Response) => {
    try {
      const { amount, description = 'Session coin purchase' } = req.body;

      if (!amount || typeof amount !== 'number' || amount <= 0) {
        return res.status(400).json({ error: 'Invalid amount' });
      }

      // For now, we'll just validate the request and return success
      // The actual coin deduction happens on the frontend via session storage
      // This endpoint can be used for logging/analytics if needed

      console.log(`💸 Session coin spend request: ${amount} coins for ${description}`);

      res.json({
        success: true,
        message: `Successfully spent ${amount / 100} DasWos coins`,
        amountSpent: amount
      });

    } catch (error) {
      console.error('Error processing session coin spend:', error);
      res.status(500).json({ error: 'Failed to process session coin spend' });
    }
  });

  // Get session coin balance (for verification)
  app.get('/api/session-coins/balance', async (req: Request, res: Response) => {
    try {
      // This is mainly for debugging - the real balance is managed on frontend
      res.json({
        message: 'Session coins are managed client-side',
        note: 'Check browser session storage for actual balance'
      });
    } catch (error) {
      console.error('Error getting session coin balance:', error);
      res.status(500).json({ error: 'Failed to get session coin balance' });
    }
  });
}

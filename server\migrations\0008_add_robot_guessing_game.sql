-- Migration: Add Robot Guessing Game Tables
-- Created: 2024-12-19
-- Description: Add tables for tracking robot guessing game progress, daily limits, and coin rewards

-- Robot Guessing Game table - tracks daily game progress per user/session
CREATE TABLE IF NOT EXISTS robot_guessing_game (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Null for anonymous users
  session_id TEXT NOT NULL, -- For tracking anonymous users
  game_date DATE NOT NULL, -- Date of the game (YYYY-MM-DD)
  total_guesses INTEGER DEFAULT 0 NOT NULL, -- Total guesses made today
  successful_guesses INTEGER DEFAULT 0 NOT NULL, -- Successful guesses today
  coins_earned INTEGER DEFAULT 0 NOT NULL, -- Total coins earned today (in cents)
  last_guess_at TIMESTAMP, -- When the last guess was made
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Ensure one game record per user/session per day
  UNIQUE(user_id, game_date),
  UNIQUE(session_id, game_date)
);

-- Individual guess records
CREATE TABLE IF NOT EXISTS robot_guesses (
  id SERIAL PRIMARY KEY,
  game_id INTEGER NOT NULL REFERENCES robot_guessing_game(id) ON DELETE CASCADE,
  guess_x INTEGER NOT NULL, -- X coordinate of guess
  guess_y INTEGER NOT NULL, -- Y coordinate of guess
  actual_x INTEGER NOT NULL, -- Actual robot X position
  actual_y INTEGER NOT NULL, -- Actual robot Y position
  distance INTEGER NOT NULL, -- Distance from guess to actual position
  is_successful BOOLEAN NOT NULL, -- Whether guess was within success threshold
  coins_awarded INTEGER DEFAULT 0 NOT NULL, -- Coins awarded for this guess (in cents)
  cost_paid INTEGER DEFAULT 0 NOT NULL, -- Cost paid for extra guess (in cents)
  created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_robot_guessing_game_user_date ON robot_guessing_game(user_id, game_date);
CREATE INDEX IF NOT EXISTS idx_robot_guessing_game_session_date ON robot_guessing_game(session_id, game_date);
CREATE INDEX IF NOT EXISTS idx_robot_guesses_game_id ON robot_guesses(game_id);
CREATE INDEX IF NOT EXISTS idx_robot_guesses_created_at ON robot_guesses(created_at);

-- Update updated_at timestamp automatically
CREATE OR REPLACE FUNCTION update_robot_guessing_game_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER robot_guessing_game_updated_at
  BEFORE UPDATE ON robot_guessing_game
  FOR EACH ROW
  EXECUTE FUNCTION update_robot_guessing_game_updated_at();

-- Comments for documentation
COMMENT ON TABLE robot_guessing_game IS 'Tracks daily robot guessing game progress and coin earnings per user/session';
COMMENT ON TABLE robot_guesses IS 'Individual guess records with coordinates, distance, and rewards';
COMMENT ON COLUMN robot_guessing_game.user_id IS 'User ID for logged-in users, NULL for anonymous users';
COMMENT ON COLUMN robot_guessing_game.session_id IS 'Session ID for tracking anonymous users';
COMMENT ON COLUMN robot_guessing_game.game_date IS 'Date of the game, resets daily';
COMMENT ON COLUMN robot_guessing_game.total_guesses IS 'Total guesses made today (max 20 free per day)';
COMMENT ON COLUMN robot_guessing_game.successful_guesses IS 'Number of successful guesses today';
COMMENT ON COLUMN robot_guessing_game.coins_earned IS 'Total DasWos coins earned today (in cents)';
COMMENT ON COLUMN robot_guesses.distance IS 'Euclidean distance from guess to actual robot position';
COMMENT ON COLUMN robot_guesses.is_successful IS 'True if distance <= 100 pixels (success threshold)';
COMMENT ON COLUMN robot_guesses.coins_awarded IS 'DasWos coins awarded for this guess (2000 cents = 20 coins)';
COMMENT ON COLUMN robot_guesses.cost_paid IS 'Cost paid for extra guess after 20 free guesses (50 cents = 0.50 coins)';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Bo<PERSON> } from 'lucide-react';
import { Button } from './ui/button';
import WhisperVoiceControl from './whisper-voice-control';

interface GlobalVoiceSearchButtonProps {
  className?: string;
}

const GlobalVoiceSearchButton: React.FC<GlobalVoiceSearchButtonProps> = ({
  className = ''
}) => {
  const [isRobotActive, setIsRobotActive] = useState(false);

  const handleSpeakToDaswos = () => {
    // First activate the robot
    const activatedEvent = new CustomEvent('activateRobot');
    window.dispatchEvent(activatedEvent);

    // Then trigger voice recording for Daswos AI
    setTimeout(() => {
      const speakEvent = new CustomEvent('startDaswosVoice');
      window.dispatchEvent(speakEvent);
    }, 500); // Small delay to let robot activate first
  };

  // Listen for robot activation/deactivation events
  useEffect(() => {
    const handleRobotActivated = () => setIsRobotActive(true);
    const handleRobotDeactivated = () => setIsRobotActive(false);

    window.addEventListener('robotActivated', handleRobotActivated);
    window.addEventListener('robotDeactivated', handleRobotDeactivated);

    return () => {
      window.removeEventListener('robotActivated', handleRobotActivated);
      window.removeEventListener('robotDeactivated', handleRobotDeactivated);
    };
  }, []);

  return (
    <div className={`fixed bottom-[20px] right-[20px] z-[1002] ${className}`}>
      <div className="flex flex-col items-end space-y-2">
        {/* Speak to Daswos Button - hide when robot is active */}
        {!isRobotActive && (
          <Button
            onClick={handleSpeakToDaswos}
            variant="outline"
            size="icon"
            className="rounded-full bg-transparent border-0 shadow-md h-16 w-16 p-0 overflow-hidden hover:scale-105 transition-transform duration-200"
            title="Speak to Daswos"
            style={{ pointerEvents: 'auto' }}
          >
            <div className="relative w-full h-full rounded-full overflow-hidden bg-gray-700 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 transition-colors duration-200">
              <img
                src="/assets/robot/daswos_redesign_correct_logo_simple.png"
                alt="Daswos Robot"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(1.1) contrast(1.1)',
                  transform: 'scale(1.2)'
                }}
              />
              {/* Overlay for better visibility */}
              <div className="absolute inset-0 bg-black/10 rounded-full"></div>
            </div>
          </Button>
        )}

        {/* Voice Search Button */}
        <WhisperVoiceControl
          className="rounded-full bg-gray-700 dark:bg-gray-700 shadow-md border-0"
          enableTextToSpeech={true}
          isAiModeEnabled={true}
        />
      </div>
    </div>
  );
};

export default GlobalVoiceSearchButton;

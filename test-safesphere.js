// Quick test to verify SafeSphere filtering is working
import { storage } from './server/storage.js';

async function testSafeSphere() {
  try {
    console.log('Testing SafeSphere filtering...');
    
    // Test with shoes search
    const shoesResults = await storage.getProducts('safesphere', 'shoes');
    console.log(`SafeSphere search for "shoes": ${shoesResults.length} results`);
    
    if (shoesResults.length > 0) {
      console.log('First result:', {
        title: shoesResults[0].title,
        trustScore: shoesResults[0].trustScore,
        identityVerified: shoesResults[0].identityVerified,
        identityVerificationStatus: shoesResults[0].identityVerificationStatus
      });
    }
    
    // Test with all SafeSphere products
    const allSafeSphere = await storage.getProducts('safesphere');
    console.log(`All SafeSphere products: ${allSafeSphere.length} results`);
    
    // Test with OpenSphere for comparison
    const allOpenSphere = await storage.getProducts('opensphere');
    console.log(`All OpenSphere products: ${allOpenSphere.length} results`);
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testSafeSphere();

-- Migration: Update user_algorithm_assignments table to support session-based user IDs
-- This allows both authenticated users (numeric IDs) and guest users (session IDs) to use the positioning system

-- First, drop the foreign key constraint if it exists
ALTER TABLE user_algorithm_assignments DROP CONSTRAINT IF EXISTS user_algorithm_assignments_user_id_users_id_fk;

-- Change the user_id column from integer to text to support session IDs
ALTER TABLE user_algorithm_assignments ALTER COLUMN user_id TYPE text USING user_id::text;

-- Update any existing numeric user IDs to string format (they'll still work)
-- No data conversion needed since PostgreSQL will handle integer->text conversion automatically

-- Add a comment to document the change
COMMENT ON COLUMN user_algorithm_assignments.user_id IS 'User identifier - can be numeric user ID for authenticated users or session_<sessionId> for guest users';
